import React, { useEffect, useState } from "react";
import { Bars3Icon } from "@heroicons/react/24/outline";
import Loader from "../../Components/Loader/Loader";
import { useAuth } from "../../Context/AuthContext";
import {
  roundShapeDiamondPrice,
  otherShapeDiamondPrice,
  roundShapeMultiplier,
  otherShapeMultiplier,
  updateRoundShapeDiamondPrice,
  updateOtherShapeDiamondPrice,
  updateRoundShapeMultiplier,
  updateOthersShapeMultiplier,
  addNewRowRoundShapeMultiplier,
  addNewRowOthersShapeMultiplier,
  addRowRoundShapeDiamond,
  addRowOtherShapeDiamond,
  addRoundshapeColumnDiamond,
  addOthershapeColumnDiamond,
} from "../../Utils/DiamondPriceCalcluationApi";
import MessageNotification from "../../Components/MessageNotification/MessageNotification";
import ManufacturingRate from "../../Components/Tables/DiamondPrice/ManufacturingRateTable";
import DiamondMultiplierPricesTable from "../../Components/Tables/DiamondPrice/DiamondMultiplierPricesTable";

export default function DiamondPrice() {
  const { toggleSidebar, isDesktop } = useAuth();
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [selectedShape, setSelectedShape] = useState("round");
  const [shapeLoading, setShapeLoading] = useState(false);
  const [manufacturingItems, setManufacturingItems] = useState([]);
  const [sellingItems, setSellingItems] = useState([]);
  const [multiplierData, setMultiplierData] = useState([]);

  const fetchManufacturingData = async (shape) => {
    setLoading(true);
    try {
      const data =
        shape === "round"
          ? await roundShapeDiamondPrice()
          : await otherShapeDiamondPrice();
      setManufacturingItems(data);
      setSellingItems(data);
    } catch {
      setErrorMessage("Failed to fetch manufacturing data.");
    }
    setLoading(false);
  };

  const fetchMultiplierData = async (shape) => {
    setLoading(true);
    try {
      const data =
        shape === "round"
          ? await roundShapeMultiplier()
          : await otherShapeMultiplier();
      setMultiplierData(data);
    } catch {
      setErrorMessage("Failed to fetch multiplier data.");
    }
    setLoading(false);
  };

  const handleShapeChange = (shape) => {
    setShapeLoading(true);
    setSelectedShape(shape);
  };

  useEffect(() => {
    fetchManufacturingData(selectedShape);
    fetchMultiplierData(selectedShape);
    setShapeLoading(false);
  }, [selectedShape]);

  const handleUpdatePrices = async (updatedPrices) => {
    try {
      const result =
        selectedShape === "round"
          ? await updateRoundShapeDiamondPrice(updatedPrices)
          : await updateOtherShapeDiamondPrice(updatedPrices);

      if (result.status === "success") {
        setSuccessMessage("Diamond prices updated successfully!");
        setTimeout(() => setSuccessMessage(""), 5000);
        await fetchManufacturingData(selectedShape);
        await fetchMultiplierData(selectedShape);
      } else {
        throw new Error(result.message || "Update failed");
      }
    } catch (error) {
      console.error("Error updating prices:", error.message);
      setErrorMessage("Error updating prices.");
      setTimeout(() => setErrorMessage(""), 5000);
    }
  };

  const handleUpdateMultiplier = async (updatedMultiplier) => {
    try {
      const result =
        selectedShape === "round"
          ? await updateRoundShapeMultiplier(updatedMultiplier)
          : await updateOthersShapeMultiplier(updatedMultiplier);

      if (result.status === 200 && result.data.status === "success") {
        setSuccessMessage("Multiplier updated successfully!");
        setTimeout(() => setSuccessMessage(""), 5000);
        await fetchMultiplierData(selectedShape);
      } else {
        throw new Error(result.data.message || "Update failed");
      }
    } catch (error) {
      console.error("Error updating multiplier:", error.message);
      setErrorMessage("Error updating multiplier.");
      setTimeout(() => setErrorMessage(""), 5000);
    }
  };

  const handleAddNewRow = async (newRow) => {
    try {
      const payload = {
        diamondType: newRow.diamondType || "Unknown",
        profitMultiplier: isNaN(parseFloat(newRow.profitMultiplier))
          ? 0
          : parseFloat(newRow.profitMultiplier),
      };

      const result =
        selectedShape === "round"
          ? await addNewRowRoundShapeMultiplier(payload)
          : await addNewRowOthersShapeMultiplier(payload);

      if (result.status === 200 && result.data.status === "success") {
        setSuccessMessage("New row added successfully!");
        setTimeout(() => setSuccessMessage(""), 5000);
        await fetchMultiplierData(selectedShape);
      } else {
        throw new Error(result.data.message || "Failed to add new row");
      }
    } catch (error) {
      console.error("Error adding new row:", error.message);
      setErrorMessage("Error adding new row.");
      setTimeout(() => setErrorMessage(""), 5000);
    }
  };

  const handleAddNewDiamondRow = async (newRows) => {
    try {
        const result =
            selectedShape === "round"
                ? await addRowRoundShapeDiamond(newRows)
                : await addRowOtherShapeDiamond(newRows);

        if (result?.status === "success" && Array.isArray(result?.data)) {
            await fetchManufacturingData(selectedShape);
            setSuccessMessage("New row added successfully!");
            setTimeout(() => setSuccessMessage(""), 5000);
        } else {
            throw new Error(result?.message || "Failed to add new row");
        }
    } catch (error) {
        console.error("Error adding new row:", error.message);
        const errorMsg = error.response?.data?.errors?.[0]?.message || "Error adding column!";
      setErrorMessage(errorMsg);
        setTimeout(() => setErrorMessage(""), 5000);
    }
};

const handleAddNewColumn = async (newColumn) => {
  try {
    const result =
      selectedShape === "round"
        ? await addRoundshapeColumnDiamond(newColumn)
        : await addOthershapeColumnDiamond(newColumn);

    if (result?.status === "success" && Array.isArray(result?.data)) {
      await fetchManufacturingData(selectedShape);
      setSuccessMessage("New column added successfully!");
      setTimeout(() => setSuccessMessage(""), 5000);
    } else {
      console.error("API Error (Invalid success response):", result);
      throw new Error(result?.message || "Failed to add new column");
    }
  } catch (error) {
    console.error("Error adding new column:", error.message);
    const errorMsg = error.response?.data?.errors?.[0]?.message || "Error adding column!";
    setErrorMessage(errorMsg);
    setTimeout(() => setErrorMessage(""), 5000);
  }
};

  return (
    <div className="flex lg:ml-72 flex-col min-h-screen">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="w-full h-auto bg-white shadow-sm p-6 overflow-hidden">
            <h1 className="text-4xl font-bold text-gray-800 mb-6">
              Studded Diamond Price Calculation
            </h1>

            <div className="mb-6 flex justify-center space-x-4">
              {["round", "other"].map((shape) => (
                <button
                  key={shape}
                  onClick={() => handleShapeChange(shape)}
                  className={`px-6 py-2 rounded-lg transition-colors cursor-pointer duration-300 ${

                    selectedShape === shape
                      ? "bg-gray-500 text-white border-black border-2 hover:border-black "
                      : "bg-gray-300 text-gray-500 hover:bg-gray-500 hover:text-white hover:border-black"
                  }`}
                >
                  {shape === "round" ? "Round Shape" : "Other Shape"}
                </button>
              ))}
            </div>

            {shapeLoading ? (
              <Loader />
            ) : (
              <div className="space-y-8">
                {successMessage && (
                  <MessageNotification
                    message={successMessage}
                    type="success"
                  />
                )}
                {errorMessage && (
                  <MessageNotification message={errorMessage} type="error" />
                )}

                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <ManufacturingRate
                    heading={"Production Price"}
                    data={manufacturingItems}
                    onUpdate={handleUpdatePrices}
                    onAddNewRow={handleAddNewDiamondRow}
                    onAddNewColumn={handleAddNewColumn}
                    priceType="manufacturingPrice"
                  />
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <DiamondMultiplierPricesTable
                    data={multiplierData}
                    onUpdate={handleUpdateMultiplier}
                    onAddNewRow={handleAddNewRow}
                  />
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <ManufacturingRate
                    heading={"Selling Price"}
                    data={sellingItems}
                    onUpdate={handleUpdatePrices}
                    priceType="sellingPrice"
                    onAddNewRow={handleAddNewDiamondRow}
                    onAddNewColumn={handleAddNewColumn}
                    hideEditButton={true}
                    hideAddNewRow={true}
                  />
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}

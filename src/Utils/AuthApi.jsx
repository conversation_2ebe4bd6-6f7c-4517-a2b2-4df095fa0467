import apiClient from "./AxiosConfig";

export const API_BASE_URL = import.meta.env.VITE_BASE_URL;

// Login API
export const loginUser = async (credentials) => {
  try {
    const response = await apiClient.post(`${API_BASE_URL}/login`, credentials, {
      headers: {
        "Content-Type": "application/json",
      },

    });

    if (response.data.status === "success") {
      // Extract user data from the nested data object
      const { user } = response.data.data;

      // Backend automatically sets authentication cookies
      // No need to manually handle tokens on frontend

      return {
        success: true,
        data: {
          user
        },
        message: response.data.message
      };
    } else {
      return {
        success: false,
        message: response.data.message || "Login failed"
      };
    }
  } catch (error) {
    console.error("Login error:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Login failed. Please try again."
    };
  }
};

// Logout API
export const logoutUser = async () => {
  try {
    // Backend handles authentication via cookies automatically
    // No need to manually send Authorization header
    await apiClient.delete(`${API_BASE_URL}/logout`);

    return {
      success: true,
      message: "Logged out successfully"
    };
  } catch (error) {
    console.error("Logout error:", error);
    // Return success even if API call fails to ensure user gets logged out
    return {
      success: true,
      message: "Logged out successfully"
    };
  }
};

// Refresh Token API
export const refreshAccessToken = async () => {
  try {
    // Backend handles authentication via cookies automatically
    // No need to manually send Authorization header or read cookies
    const response = await apiClient.put(`${API_BASE_URL}/update-refresh-access`, {});

    if (response.data.status === "success") {
      // Backend automatically updates cookies
      // No need to manually handle tokens on frontend
      return {
        success: true,
        message: "Token refreshed successfully"
      };
    } else {
      throw new Error("Token refresh failed");
    }
  } catch (error) {
    console.error("Token refresh error:", error);
    return {
      success: false,
      message: "Session expired. Please login again."
    };
  }
};

// Helper function to check if user is authenticated
// Since backend handles cookies, we'll check authentication via API call
export const isAuthenticated = async () => {
  try {
    // Make a simple API call to any protected endpoint to check if user is authenticated
    // Backend will validate the cookie automatically
    // Using metal-price endpoint as it's likely a protected route
    const response = await apiClient.get('/metal-price');
    return response.status === 200;
  } catch (error) {
    // If API call fails (401, 403, etc.), user is not authenticated
    return false;
  }
};

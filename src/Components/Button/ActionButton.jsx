import React from "react";

export default function ActionButton({ onClick, isLoading, isEditing, text, onDiscard }) {
  return (
    <div className="flex gap-4">
      {isEditing && (
        <button
          onClick={onDiscard}
          className="px-6 py-3 border cursor-pointer border-gray-400 rounded-lg bg-red-200 text-red-700 hover:bg-red-300 transition text-lg font-semibold shadow-lg"
        >
          Discard Changes
        </button>
      )}
      <button
        onClick={onClick}
        disabled={isLoading}
        className={`px-6 py-3 border-gray-400 cursor-pointer rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-100 transition text-lg font-semibold shadow-lg ${
          isLoading ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        {isLoading ? "Updating..." : isEditing ? `Save ${text}` : `Edit ${text}`}
      </button>
    </div>
  );
}

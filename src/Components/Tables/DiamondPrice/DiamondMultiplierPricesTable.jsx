import React, { useState, useEffect } from "react";
import ActionButton from "../../Button/ActionButton";
import ConfirmModal from "../../Modal/ConfirmModal";
import MessageNotification from "../../MessageNotification/MessageNotification";
import DynamicRow from "../../DynamicRow/DynamicRow";

export default function DiamondMultiplier({ data, onUpdate, onAddNewRow }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editableData, setEditableData] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [error, setError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [isAddingRow, setIsAddingRow] = useState(false);

  useEffect(() => {
    setEditableData([...data]);
  }, [data]);

  const handleEditClick = () => {
    isEditing ? setModalOpen(true) : setIsEditing(true);
  };

  const handleMultiplierChange = (id, value) => {
    setEditableData((prevData) =>
      prevData.map((item) =>
        item._id === id
          ? { ...item, profitMultiplier: value ? parseFloat(value) : "" }
          : item
      )
    );
  };

  const handleDiamondTypeChange = (id, value) => {
    setEditableData((prevData) =>
      prevData.map((item) =>
        item._id === id ? { ...item, diamondType: value } : item
      )
    );
  };

  const handleConfirmChanges = () => {
    setModalOpen(false);
    setButtonLoader(true);

    const updatedData = editableData
      .filter((item) => {
        const originalItem = data.find((d) => d._id === item._id);
        return (
          originalItem &&
          originalItem.profitMultiplier !== item.profitMultiplier
        );
      })
      .map((item) => ({
        id: item._id,
        profitMultiplier: item.profitMultiplier,
      }));

    if (updatedData.length > 0) {
      onUpdate(updatedData);
    }

    setIsEditing(false);
    setButtonLoader(false);
  };

  const handleCancelChanges = () => {
    setEditableData([...data]);
    setIsEditing(false);
    setModalOpen(false);
  };

  const handleAddRowClick = (newRowData) => {
    const newRow = {
      _id: Date.now().toString(),
      diamondType: newRowData.diamondType || "",
      profitMultiplier: newRowData.profitMultiplier || "",
    };

    setEditableData((prevData) => [...prevData, newRow]);

    if (onAddNewRow) {
      onAddNewRow(newRow);
    }
    setIsAddingRow(false);
  };

  return (
    <div className="px-5 pt-2 pb-8">
      {successMessage && (
        <MessageNotification type="success" message={successMessage} />
      )}
      {error && (
        <MessageNotification
          type="error"
          message="Error updating profit multipliers"
        />
      )}

      <div className="flex justify-end mt-6 pb-4">
        <ActionButton
          onClick={handleEditClick}
          onDiscard={handleCancelChanges}
          isLoading={buttonLoader}
          isEditing={isEditing}
          text="Profit Multipliers"
        />
      </div>

      {/* Table Container */}
      <div className="flex justify-center">
        <div className="overflow-x-auto border border-gray-300 rounded-lg shadow-md min-w-[50%]">
          <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-center bg-gray-200">
                Diamond Color-Clarity
              </th>
              <th className="px-6 py-3 text-center bg-gray-200">
                Diamond Price Profit Multiplier
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-yellow-100 text-center">
            {editableData.map((item) => (
              <tr key={item._id}>
                <td className="p-4 font-semibold text-center bg-gray-50">
                  {item.diamondType}
                </td>
                <td className="p-4 text-center">
                  {isEditing ? (
                    <input
                      type="number"
                      className="w-20 p-2 border rounded-md text-center"
                      value={
                        item.profitMultiplier !== ""
                          ? item.profitMultiplier
                          : ""
                      }
                      onChange={(e) =>
                        handleMultiplierChange(item._id, e.target.value)
                      }
                    />
                  ) : (
                    `${item.profitMultiplier}`
                  )}
                </td>
              </tr>
            ))}

            {/* Dynamic Row for Adding New Data */}
            {isAddingRow && (
              <tr>
                <td colSpan="2">
                  <DynamicRow
                    columns={[
                      {
                        key: "diamondType",
                        placeholder: "Diamond Color-Clarity",
                      },
                      {
                        key: "profitMultiplier",
                        placeholder: "Diamond Price Profit Multiplier",
                        type: "number",
                      },
                    ]}
                    onAddRow={(rowData) => handleAddRowClick(rowData)}
                    onCancel={() => setIsAddingRow(false)}
                  />
                </td>
              </tr>
            )}
          </tbody>
          </table>
        </div>
      </div>

      {/* Centered Add Row Button Below the Table */}
      <div className="flex justify-center mt-4">
        {!isAddingRow && (
          <button
            onClick={() => setIsAddingRow(true)}
            className="cursor-pointer px-4 py-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300"
          >
            Add New Row
          </button>
        )}
      </div>

      {/* Confirmation Modal */}
      {modalOpen && (
        <ConfirmModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          onConfirm={handleConfirmChanges}
          title="Confirm Changes"
          message="Are you sure you want to update profit multipliers?"
        />
      )}
    </div>
  );
}

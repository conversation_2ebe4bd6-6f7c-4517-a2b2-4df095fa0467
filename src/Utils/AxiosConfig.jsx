import axios from "axios";
import { refreshAccessToken } from "./AuthApi";

const API_BASE_URL = process.env.VITE_BASE_URL;

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true
});

// Request interceptor - no need to manually add auth tokens
// Backend handles authentication via cookies automatically
apiClient.interceptors.request.use(
  (config) => {
    // Cookies are automatically sent with requests due to withCredentials: true
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Check if this is a login or logout endpoint
    const isLoginEndpoint = originalRequest.url?.includes('/login');
    const isLogoutEndpoint = originalRequest.url?.includes('/logout');
    const isRefreshEndpoint = originalRequest.url?.includes('/update-refresh-access');

    // If the error is 401 and we haven't already tried to refresh
    // and it's not a login/logout/refresh endpoint
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !isLoginEndpoint &&
      !isLogoutEndpoint &&
      !isRefreshEndpoint
    ) {
      originalRequest._retry = true;

      try {
        const refreshResult = await refreshAccessToken();

        if (refreshResult.success) {
          // Backend automatically updates cookies, just retry the original request
          return apiClient(originalRequest);
        } else {
          // Refresh failed, redirect to login
          window.location.href = "/login";
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        window.location.href = "/login";
        return Promise.reject(error);
      }
    }

    // For login/logout/refresh endpoints or other errors, just reject
    return Promise.reject(error);
  }
);

export default apiClient;

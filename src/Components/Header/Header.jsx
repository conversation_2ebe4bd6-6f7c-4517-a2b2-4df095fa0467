import React, { useState } from "react";
import { Bars3Icon, UserCircleIcon, ArrowRightOnRectangleIcon } from "@heroicons/react/24/outline";
import { useAuth } from "../../Context/AuthContext";

const Header = () => {
  const { toggleSidebar, isDesktop, user, logout } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleSyncClick = async () => {
    try {
      await fetch(`${import.meta.env.VITE_BASE_URL}/sync`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
    } catch (error) {
      console.error("Sync failed:", error);
    }
  };

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <div className="w-full p-[10px] h-[60px] bg-gray-200 shadow-md flex items-center justify-between">
      <div>
        {!isDesktop && (
          <button
            onClick={toggleSidebar}
            className="p-2 cursor-pointer rounded-md hover:bg-gray-300 transition"
          >
            <Bars3Icon className="h-6 w-6 text-gray-700" />
          </button>
        )}
      </div>

      <div className="flex items-center space-x-4">
        <button
          onClick={handleSyncClick}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 flex items-center cursor-pointer"
        >
          Sync Products
        </button>

        {/* User info and logout */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <UserCircleIcon className="h-8 w-8 text-gray-700" />
            {user && (
              <div className="hidden md:block">
                <p className="text-sm font-medium text-gray-700">{user.name}</p>
                <p className="text-xs text-gray-500">{user.role}</p>
              </div>
            )}
          </div>

          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className={`flex items-center space-x-1 px-3 py-2 rounded-md transition-colors duration-200 ${
              isLoggingOut
                ? "bg-gray-300 cursor-not-allowed"
                : "bg-red-500 hover:bg-red-600 text-white"
            }`}
          >
            <ArrowRightOnRectangleIcon className="h-4 w-4" />
            <span className="text-sm">
              {isLoggingOut ? "Logging out..." : "Logout"}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Header; 
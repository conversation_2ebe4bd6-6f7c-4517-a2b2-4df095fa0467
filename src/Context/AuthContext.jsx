import { createContext, useContext, useState, useEffect } from "react";
import { isAuthenticated, logoutUser } from "../Utils/AuthApi";

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
	const [isLoggedIn, setIsLoggedIn] = useState(false);
	const [user, setUser] = useState(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 1024);

	// Check authentication status on app load
	useEffect(() => {
		const checkAuthStatus = async () => {
			try {
				const authenticated = await isAuthenticated();
				setIsLoggedIn(authenticated);
			} catch (error) {
				console.error("Auth check failed:", error);
				setIsLoggedIn(false);
			} finally {
				setIsLoading(false);
			}
		};

		checkAuthStatus();
	}, []);

	const login = (userData) => {
		setIsLoggedIn(true);
		setUser(userData);
	};

	const logout = async () => {
		try {
			await logoutUser();
		} catch (error) {
			console.error("Logout error:", error);
		} finally {
			setIsLoggedIn(false);
			setUser(null);
			// Backend handles cookie cleanup automatically
		}
	};

	const toggleSidebar = () => setIsSidebarOpen((prev) => !prev);

	// Update isDesktop when the window resizes
	useEffect(() => {
		const handleResize = () => {
			setIsDesktop(window.innerWidth >= 1024);
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	return (
		<AuthContext.Provider value={{
			isLoggedIn,
			user,
			isLoading,
			login,
			logout,
			isSidebarOpen,
			toggleSidebar,
			isDesktop
		}}>
			{children}
		</AuthContext.Provider>
	);
};

export const useAuth = () => useContext(AuthContext);

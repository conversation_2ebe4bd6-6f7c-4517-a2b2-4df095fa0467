import React, { useState } from "react";

export default function AddColumn({ 
  diamondTypes, 
  onAdd, 
  onCancel, 
  isGradeMode = false,  
  gradeRows = []      
}) {
  const [minSize, setMinSize] = useState("");
  const [maxSize, setMaxSize] = useState("");
  const [prices, setPrices] = useState({});
  const [gradeName, setGradeName] = useState(""); 
  

  // Handle price input change
  const handlePriceChange = (index, value) => {
    setPrices((prev) => ({
      ...prev,
      [index]: parseFloat(value) || 0,
    }));
  };

  // Handle form submission
  const handleSubmit = () => {
    if (isGradeMode) {
      if (!gradeName.trim()) {
        alert("Please enter a grade name.");
        return;
      }
      
      const newGrade = {
        grade: gradeName,
        data: gradeRows.map((row, index) => ({
          minCarat: row.minCarat,
          maxCarat: row.maxCarat,
          price: prices[index] || 0, 
        })),
      };
      onAdd(newGrade);
    } else {
      if (!minSize || !maxSize) {
        alert("Please enter min and max size.");
        return;
      }

      const newColumn = {
        minSize: parseFloat(minSize),
        maxSize: parseFloat(maxSize),
        prices,
      };

      onAdd(newColumn);
    }
  };

  return (
    <div className="p-4 bg-white shadow-md rounded-md w-full max-w-lg">
      <h3 className="text-lg font-semibold mb-3 text-gray-700">
        {isGradeMode ? "Add New Grade" : "Add New Column"}
      </h3>

      {/* Grade Name Input (Only in Grade Mode) */}
      {isGradeMode && (
        <div className="mb-3">
          <label className="block text-sm font-medium text-gray-600">Grade Name</label>
          <input
            type="text"
            value={gradeName}
            onChange={(e) => setGradeName(e.target.value)}
            placeholder="Enter grade name"
            className="w-full p-2 border rounded-md text-sm"
          />
        </div>
      )}

      {/* Min & Max Size Inputs (Only in Column Mode) */}
      {!isGradeMode && (
        <div className="flex gap-4 mb-3">
          <div className="w-1/2">
            <label className="block text-sm font-medium text-gray-600">Min Size</label>
            <input
              type="number"
              value={minSize}
              onChange={(e) => setMinSize(e.target.value)}
              placeholder="Enter min size"
              className="w-full p-2 border rounded-md text-sm"
            />
          </div>
          <div className="w-1/2">
            <label className="block text-sm font-medium text-gray-600">Max Size</label>
            <input
              type="number"
              value={maxSize}
              onChange={(e) => setMaxSize(e.target.value)}
              placeholder="Enter max size"
              className="w-full p-2 border rounded-md text-sm"
            />
          </div>
        </div>
      )}

      {/* Price Inputs (Dynamic Based on Mode) */}
      {isGradeMode ? (
        gradeRows.length > 0 ? (
          gradeRows.map((row, index) => (
            <div key={index} className="mb-3 flex flex-wrap">
              <label className="block text-sm font-medium text-gray-600">
                {row.minCarat} - {row.maxCarat} Carat Price
              </label>
              <input
                type="number"
                value={prices[index] || ""}
                onChange={(e) => handlePriceChange(index, e.target.value)}
                placeholder="Enter price"
                className="w-full p-2 border rounded-md text-sm"
              />
            </div>
          ))
        ) : (
          <p className="text-red-500">No rows available.</p>
        )
      ) : (
        diamondTypes?.map((diamondType, index) => (
          <div key={index} className="mb-3">
            <label className="block text-sm font-medium text-gray-600">
              {diamondType} Price
            </label>
            <input
              type="number"
              value={prices[diamondType] || ""}
              onChange={(e) => handlePriceChange(diamondType, e.target.value)}
              placeholder={`Enter ${diamondType} price`}
              className="w-full p-2 border rounded-md text-sm"
            />
          </div>
        ))
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 mt-4">
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
        >
          {isGradeMode ? "Add Grade" : "Add Column"}
        </button>
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

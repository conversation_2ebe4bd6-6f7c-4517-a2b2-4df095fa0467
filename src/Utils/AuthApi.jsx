// import axios from "axios";
import Cookies from "js-cookie";
import apiClient from "./AxiosConfig";

export const API_BASE_URL = import.meta.env.VITE_BASE_URL;

// Login API
export const loginUser = async (credentials) => {
  try {
    const response = await apiClient.post(`${API_BASE_URL}/login`, credentials, {
      headers: {
        "Content-Type": "application/json",
      },

    });

    if (response.data.status === "success") {
      // Extract tokens from the nested data object
      const { accessToken, refreshToken, user } = response.data.data;
      
      // Store tokens in cookies
      // Cookies.set("accessToken", accessToken, {
      //   expires: 1, // 1 day
      //   secure: true,
      //   sameSite: "strict"
      // });

      // Cookies.set("refreshToken", refreshToken, {
      //   expires: 30, // 30 days
      //   secure: true,
      //   sameSite: "strict"
      // });

      return {
        success: true,
        data: {
          user,
          accessToken,
          refreshToken
        },
        message: response.data.message
      };
    } else {
      return {
        success: false,
        message: response.data.message || "Login failed"
      };
    }
  } catch (error) {
    console.error("Login error:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Login failed. Please try again."
    };
  }
};

// Logout API
export const logoutUser = async () => {
  try {
    const accessToken = Cookies.get("accessToken");
    
    if (!accessToken) {
      // If no token, just clear cookies and return success
      // clearAuthTokens();
      return { success: true };
    }

    const response = await apiClient.delete(`${API_BASE_URL}/logout`, {
      headers: {
        "Authorization": `Bearer ${accessToken}`
        // Cookie header is automatically handled by the browser
      },
    });

    // Clear tokens regardless of API response
    // clearAuthTokens();
    
    return {
      success: true,
      message: "Logged out successfully"
    };
  } catch (error) {
    console.error("Logout error:", error);
    // Clear tokens even if API call fails
    // clearAuthTokens();
    return {
      success: true,
      message: "Logged out successfully"
    };
  }
};

// Refresh Token API
export const refreshAccessToken = async () => {
  try {
    const refreshToken = Cookies.get("refreshToken");
    const accessToken = Cookies.get("accessToken");
    
    if (!refreshToken) {
      throw new Error("No refresh token available");
    }

    const response = await apiClient.put(`${API_BASE_URL}/update-refresh-access`, {}, {
      headers: {
        "Authorization": `Bearer ${refreshToken}`
        // Cookie header is automatically handled by the browser
      },
    });

    if (response.data.status === "success") {
      const { accessToken: newAccessToken, refreshToken: newRefreshToken } = response.data.data;
      
      // Update tokens in cookies
      // Cookies.set("accessToken", newAccessToken, {
      //   expires: 1,
      //   secure: true,
      //   sameSite: "strict"
      // });

      // if (newRefreshToken) {
      //   Cookies.set("refreshToken", newRefreshToken, {
      //     expires: 30,
      //     secure: true,
      //     sameSite: "strict"
      //   });
      // }

      return {
        success: true,
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      };
    } else {
      throw new Error("Token refresh failed");
    }
  } catch (error) {
    console.error("Token refresh error:", error);
    // clearAuthTokens();
    return {
      success: false,
      message: "Session expired. Please login again."
    };
  }
};

// Helper function to clear all auth tokens
export const clearAuthTokens = () => {
  Cookies.remove("accessToken");
  Cookies.remove("refreshToken");
};

// Helper function to get current access token
export const getAccessToken = () => {
  return Cookies.get("accessToken");
};

// Helper function to get current refresh token
export const getRefreshToken = () => {
  return Cookies.get("refreshToken");
};

// Helper function to check if user is authenticated
export const isAuthenticated = () => {
  const accessToken = getAccessToken();
  return !!accessToken;
};

import React, { useEffect, useState } from "react";
import { useAuth } from "../../Context/AuthContext";
import { Bars3Icon } from "@heroicons/react/24/outline";
import ConfirmModal from "../../Components/Modal/ConfirmModal";
import Loader from "../../Components/Loader/Loader";
import ActionButton from "../../Components/Button/ActionButton";
import MessageNotification from "../../Components/MessageNotification/MessageNotification";
import Pagination from "../../Components/Pagination/Pagination";
import DynamicRow from "../../Components/DynamicRow/DynamicRow";
import { getMetalPrices, updateHandlingCharges, addMetalPrice } from '../../Utils/MetalPriceCalculationApi'

export default function MetalPriceCalculation() {
  const {toggleSidebar, isDesktop } = useAuth();
  const [metalData, setMetalData] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [updatedCharges, setUpdatedCharges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [error, setError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [previousData, setPreviousData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddingRow, setIsAddingRow] = useState(false);
  const [dataUpdated, setDataUpdated] = useState(false);
  const itemsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
        setLoading(true);
        try {
            const data = await getMetalPrices();
            setMetalData(data);
        } catch {
            console.error(error.response?.data || error.message || "Error Occured");
        } finally {
            setLoading(false);
        }
    };
    fetchData();
}, [dataUpdated]);

const handleAddRow = async (newRowData) => {
  setIsAddingRow(false);

  try {
    setLoading(true);
    setSuccessMessage(null);
    setError(null);

    const { metalName, metalPurity, fetchedPrice, ...filteredData } = newRowData;

    const capitalizedMetalName = metalName.charAt(0).toUpperCase() + metalName.slice(1).toLowerCase();

    const uppercaseMetalPurity = metalPurity.toUpperCase();

    const updatedData = {
      ...filteredData,
      metalName: capitalizedMetalName,
      metalPurity: uppercaseMetalPurity,
    };

    const response = await addMetalPrice(updatedData);

    setDataUpdated((prev) => !prev);

    setSuccessMessage(response?.message);
  } catch (errors) {
    console.error("API Error:", errors.response?.data || errors.message);
    handleError(errors);
  } finally {
    setLoading(false);
    setTimeout(() => {
      setSuccessMessage(null);
      setError(null);
    }, 5000);
  }
};

const handleError = (error) => {
  // Extract the message from the error response
  const errorMessage = error?.response?.data?.errors?.[0]?.message || error?.message || "Error Occurred";
  setError(errorMessage);  // Store the actual error message
  setTimeout(() => {
    setError(null); 
  }, 5000);
};

  const handleEditClick = () => {
    if (!isEditing) {
      setPreviousData([...metalData]);
    } else {
      setModalOpen(true);
    }
    setIsEditing(!isEditing);
  };

  const handleChargeChange = (id, value) => {
    setMetalData((prevData) =>
      prevData.map((item) =>
        item._id === id
          ? { ...item, handlingCharge: parseFloat(value) || 0 }
          : item
      )
    );
    setUpdatedCharges((prev) => [
      ...prev.filter((item) => item.id !== id),
      { id, handlingCharge: parseFloat(value) || 0 },
    ]);
  };

  const handleConfirmChanges = async () => {
    setModalOpen(false);
    setButtonLoader(true);
    console.log("Final updatedCharges before API call: ", updatedCharges);

    try {
      const response = await updateHandlingCharges([...updatedCharges]);
  
      if (response) {
        setIsEditing(false);
        setUpdatedCharges([]);
        setSuccessMessage("Handling charges updated successfully!");
  
        setTimeout(() => {
          setSuccessMessage("");
        }, 5000);
      } else {
        throw new Error("Failed to update!");
      }
    } catch (error) {
      setMetalData(previousData);
      setUpdatedCharges([]);
      handleError(error);
    } finally {
      setButtonLoader(false);
    }
  };
  

  const handleCancelChanges = () => {
    setMetalData(previousData);
    setUpdatedCharges([]);
    setIsEditing(false);
    setModalOpen(false);
    setIsAddingRow(false); // Close the add row state
  };

  const totalPages = Math.ceil(metalData.length / itemsPerPage);
  const currentItems = metalData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="flex lg:ml-72 flex-col">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="w-full h-[100vh] bg-white shadow-sm p-6">
            <h1 className="text-4xl font-bold text-gray-800 mb-6">
              Metal Price Calculation
            </h1>

            <MessageNotification message={successMessage} type="success" />
            {error && (
              <MessageNotification message={error} type="error" />
            )}

            <div className="flex justify-end mt-6 pb-8">
              <ActionButton
                onClick={handleEditClick}
                onDiscard={handleCancelChanges}
                isLoading={buttonLoader}
                isEditing={isEditing}
                text="Handling Charges"
              />
            </div>

            <div className="overflow-x-auto pt-2 pb-8">
              <table className="min-w-full divide-y divide-gray-300 border border-gray-300 shadow-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-6 py-3 text-center">Metal</th>
                    <th className="px-6 py-3 text-center">Metal Purity</th>
                    <th className="px-6 py-3 text-center">Fetched Price</th>
                    <th className="px-6 py-3 text-center bg-yellow-100">
                      Handling Charge (%)
                    </th>
                    <th className="px-6 py-3 text-center">
                      Total Metal Cost (₹/gm)
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white text-center">
                  {currentItems.map((item) => {
                    const totalCost =
                      item.fetchedPrice * (1 + item.handlingCharge / 100);
                    return (
                      <tr key={item._id}>
                        <td className="p-4 text-center">{item.metalName}</td>
                        <td className="p-4 text-center">{item.metalPurity}</td>
                        <td className="p-4 text-center">
                          ₹{item.fetchedPrice}
                        </td>
                        <td className="p-4 bg-yellow-50 text-center">
                          {isEditing ? (
                            <input
                              type="number"
                              className="w-20 p-2 border rounded-md text-center"
                              value={item.handlingCharge}
                              onChange={(e) =>
                                handleChargeChange(item._id, e.target.value)
                              }
                            />
                          ) : (
                            <span>{item.handlingCharge}%</span>
                          )}
                        </td>
                        <td className="p-4 text-center">
                          ₹{totalCost.toFixed(2)}
                        </td>
                      </tr>
                    );
                  })}

                  {isAddingRow && (
                    <tr>
                      <td colSpan="5" className="p-4 text-center">
                        <DynamicRow
                          columns={[
                            {
                              key: "metalName",
                              placeholder: "Enter Gold or Platinum",
                              type: "text",
                            },
                            {
                              key: "metalPurity",
                              placeholder: "Enter Metal Purity",
                              type: "text",
                            },
                            {
                              key: "handlingCharge",
                              placeholder: "Enter Handling Charge",
                              type: "number",
                            },
                          ]}
                          onAddRow={handleAddRow}
                          onCancel={() => setIsAddingRow(false)}
                        />
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            <div className="flex justify-start mt-6 pb-8 pl-4">
              <button
                className="ml-4 cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300"
                onClick={() => setIsAddingRow(true)}
              >
                Add New Row
              </button>
            </div>

            {metalData.length > 10 && (
              <Pagination
                totalItems={metalData.length}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
          </div>

          {modalOpen && (
        <ConfirmModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          onConfirm={handleConfirmChanges}
        />
      )}
        </>
      )}
    </div>
  );
}

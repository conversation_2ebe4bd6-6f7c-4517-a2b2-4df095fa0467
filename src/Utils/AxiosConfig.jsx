import axios from "axios";
import { getAccessToken, refreshAccessToken, } from "./AuthApi";

const API_BASE_URL = import.meta.env.VITE_BASE_URL;

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      // Don't set Cookie header manually - browsers handle this automatically
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 and we haven't already tried to refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshResult = await refreshAccessToken();
        
        if (refreshResult.success) {
          // Update the original request with new token
          originalRequest.headers.Authorization = `Bearer ${refreshResult.accessToken}`;
          // Don't set Cookie header manually - browsers handle this automatically

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          // Refresh failed, redirect to login
          // clearAuthTokens();
          window.location.href = "/login";
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        // clearAuthTokens();
        window.location.href = "/login";
        return Promise.reject(error);
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;

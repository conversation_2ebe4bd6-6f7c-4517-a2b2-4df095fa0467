import apiClient from "./AxiosConfig";
impr

export const API_BASE_URL = import.meta.env.VITE_BASE_URL;

// Login API
export const loginUser = async (credentials) => {
  try {
    const response = await apiClient.post(`${API_BASE_URL}/login`, credentials, {
      headers: {
        "Content-Type": "application/json",
      },

    });

    if (response.data.status === "success") {
      // Extract user data from the nested data object
      const { user } = response.data.data;

      // Backend automatically sets authentication cookies
      // No need to manually handle tokens on frontend

      return {
        success: true,
        data: {
          user
        },
        message: response.data.message
      };
    } else {
      return {
        success: false,
        message: response.data.message || "Login failed"
      };
    }
  } catch (error) {
    console.error("Login error:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Login failed. Please try again."
    };
  }
};

// Logout API
export const logoutUser = async () => {
  try {
    // Backend handles authentication via cookies automatically
    // No need to manually send Authorization header
    await apiClient.delete(`${API_BASE_URL}/logout`);

    return {
      success: true,
      message: "Logged out successfully"
    };
  } catch (error) {
    console.error("Logout error:", error);
    // Return success even if API call fails to ensure user gets logged out
    return {
      success: true,
      message: "Logged out successfully"
    };
  }
};

// Refresh Token API
export const refreshAccessToken = async () => {
  try {
    // Backend handles authentication via cookies automatically
    // No need to manually send Authorization header or read cookies
    const response = await axios.put(`${API_BASE_URL}/update-refresh-access`, {});

    if (response.data.status === "success") {
      // Backend automatically updates cookies
      // No need to manually handle tokens on frontend
      return {
        success: true,
        message: "Token refreshed successfully"
      };
    } else {
      throw new Error("Token refresh failed");
    }
  } catch (error) {
    console.error("Token refresh error:", error);
    return {
      success: false,
      message: "Session expired. Please login again."
    };
  }
};

// Helper function to check if user is authenticated
// Since backend handles cookies, we'll assume user is not authenticated initially
// and let the protected routes handle authentication checks
export const isAuthenticated = () => {
  // For backend-only cookie management, we'll start with false
  // and let the app determine authentication state through actual API calls
  // This prevents unnecessary token refresh calls on initial load
  return false;
};

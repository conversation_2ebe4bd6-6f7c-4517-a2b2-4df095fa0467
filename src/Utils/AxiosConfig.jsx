import axios from "axios";

const API_BASE_URL = import.meta.env.VITE_BASE_URL;

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true
});

// Request interceptor - no need to manually add auth tokens
// Backend handles authentication via cookies automatically
apiClient.interceptors.request.use(
  (config) => {
    // Cookies are automatically sent with requests due to withCredentials: true
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // For 401 errors on protected endpoints (not login/logout), redirect to login
    const isLoginEndpoint = error.config?.url?.includes('/login');
    const isLogoutEndpoint = error.config?.url?.includes('/logout');

    if (error.response?.status === 401 && !isLoginEndpoint && !isLogoutEndpoint) {
      // Only redirect if we're not already on the login page
      if (window.location.pathname !== '/login') {
        window.location.href = "/login";
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;

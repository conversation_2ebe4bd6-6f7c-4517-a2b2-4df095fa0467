{"name": "diamond-hq-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint .", "preview": "vite preview --port 1337 --host"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@tailwindcss/vite": "^4.0.8", "axios": "^1.8.0", "dotenv": "^16.4.7", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.2.0", "tailwindcss": "^4.0.8"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "babel-jest": "^29.7.0", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "vite": "^6.2.0"}}
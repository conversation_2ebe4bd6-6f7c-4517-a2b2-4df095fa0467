import React, { useState, useEffect } from "react";
import ActionButton from "../../Button/ActionButton";
import ConfirmModal from "../../Modal/ConfirmModal";
import MessageNotification from "../../MessageNotification/MessageNotification";
import AddRow from "../../EditTables/AddRow";
import AddColumn from "../../EditTables/AddColumn";

export default function ManufacturingRate({
  heading,
  data,
  onUpdate,
  onAddNewRow,
  onAddNewColumn,
  priceType = "manufacturingPrice",
  hideEditButton,
  hideAddNewRow,
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [editableData, setEditableData] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [showAddRow, setShowAddRow] = useState(false);
  const [showAddColumn, setShowAddColumn] = useState(false);
  const [sieveSizes, setSieveSizes] = useState([]);

  useEffect(() => {
    setEditableData([...data]);
    const uniqueSizes = [
      ...new Set(data.map((item) => `${item.minSize}-${item.maxSize}`)),
    ];
    setSieveSizes(uniqueSizes);
  }, [data]);

  const diamondTypes = Array.from(
    new Set(editableData.map((item) => item.diamondType))
  );

  const handleEditClick = () => {
    isEditing ? setModalOpen(true) : setIsEditing(true);
  };

  const handlePriceChange = (type, size, value) => {
    setEditableData((prevData) =>
      prevData.map((item) =>
        item.diamondType === type && `${item.minSize}-${item.maxSize}` === size
          ? { ...item, [priceType]: parseFloat(value) || 0 }
          : item
      )
    );
  };

  const handleConfirmChanges = async () => {
    setModalOpen(false);
    setButtonLoader(true);

    const updatedPrices = editableData.map((item) => ({
      id: item._id,
      [priceType]: item[priceType],
    }));

    try {
      setIsEditing(false);
      setSuccessMessage("Price updated successfully!");
      onUpdate(updatedPrices);
    } catch (error) {
      console.error("Error updating prices:", error);
      setErrorMessage("Error updating prices");
    } finally {
      setButtonLoader(false);

      setTimeout(() => {
        setSuccessMessage(null);
        setErrorMessage(null);
      }, 5000);
    }
  };

  const handleCancelChanges = () => {
    setEditableData([...data]);
    setIsEditing(false);
    setModalOpen(false);
  };

  const handleAddRow = async (newRows) => {
    const rowsToAdd = Array.isArray(newRows) ? newRows : [newRows];

    const updatedRows = rowsToAdd.flatMap((row) =>
      row.sizes.map((size) => ({
        diamondType: row.diamondType || "New Type",
        minSize: parseFloat(size.minSize) || 0,
        maxSize: parseFloat(size.maxSize) || 0,
        manufacturingPrice: parseFloat(size.manufacturingPrice) || 0,
      }))
    );

    try {
      await onAddNewRow(updatedRows);
    } catch (error) {
      console.error("Error adding row:", error);
      const errorMsg = error.response?.data?.errors?.[0]?.message || "Error adding column!";
      setErrorMessage(errorMsg);
      setTimeout(() => setErrorMessage(null), 5000);
    }

    setShowAddRow(false);
  };

  const handleAddColumn = async (newColumn) => {
    const { minSize, maxSize, prices } = newColumn;
    const sizeRange = `${minSize}-${maxSize}`;

    const newData = diamondTypes.map((diamondType) => ({
      diamondType,
      minSize,
      maxSize,
      [priceType]: prices[diamondType] || 0, // Store price for each diamond type
    }));

    try {
      await onAddNewColumn(newData);
    } catch (error) {
      console.error("Error adding column:", error);
      const errorMsg = error.response?.data?.errors?.[0]?.message || "Error adding column!";
      setErrorMessage(errorMsg);
      setTimeout(() => setErrorMessage(null), 5000);
    }

    setShowAddColumn(false);
  };

  return (
    <div className="px-5 pt-2 pb-8">
      {successMessage && (
        <MessageNotification type="success" message={successMessage} />
      )}
      {errorMessage && (
        <MessageNotification type="error" message={errorMessage} />
      )}


      {!hideEditButton && (
        <div className="flex justify-end mt-6 pb-8">
          <ActionButton
            onClick={handleEditClick}
            onDiscard={handleCancelChanges}
            isLoading={buttonLoader}
            isEditing={isEditing}
            text="Prices"
            />
        </div>
      )}

      {heading && (
        <h3 className="text-2xl font-bold text-gray-800 mb-6">
          {heading}
        </h3>
      )}

      <div className="overflow-x-auto border border-gray-300 rounded-lg shadow-md">
        <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-6 py-3 text-center bg-gray-200">Sieve Sizes</th>
            {sieveSizes.map((size) => (
              <th key={size} className="px-6 py-3 text-center bg-gray-200">
                {size}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-yellow-100 text-center">
          {diamondTypes.map((type, index) => (
            <tr key={`${type}-${index}`}>
              <td className="p-4 font-semibold text-center bg-gray-50">
                {type}
              </td>
              {sieveSizes.map((size) => (
                <td key={`${type}-${size}`} className="p-4 text-center">
                  {isEditing ? (
                    <input
                      type="number"
                      className="w-20 p-2 border rounded-md text-center"
                      value={
                        editableData.find(
                          (item) =>
                            item.diamondType === type &&
                            `${item.minSize}-${item.maxSize}` === size
                        )?.[priceType] || ""
                      }
                      onChange={(e) =>
                        handlePriceChange(type, size, e.target.value)
                      }
                    />
                  ) : (
                    `₹${
                      editableData
                        .find(
                          (item) =>
                            item.diamondType === type &&
                            `${item.minSize}-${item.maxSize}` === size
                        )
                        ?.[priceType]?.toLocaleString() || "-"
                    }`
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
        </table>
      </div>

      {!hideAddNewRow && (
        <div className="flex justify-center gap-4 mt-4">
          {!showAddColumn && (
            <button
              className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300"
              onClick={() => setShowAddRow(true)}
            >
              Add New Row
            </button>
          )}

          {!showAddRow && (
            <button
              className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300"
              onClick={() => setShowAddColumn(true)}
            >
              Add New Column
            </button>
          )}
        </div>
      )}

      {showAddRow && (
        <AddRow
          sieveSizes={sieveSizes}
          onAdd={handleAddRow}
          onCancel={() => setShowAddRow(false)}
        />
      )}
      {showAddColumn && (
        <AddColumn
          diamondTypes={diamondTypes}
          onAdd={handleAddColumn}
          onCancel={() => setShowAddColumn(false)}
        />
      )}

      {modalOpen && (
        <ConfirmModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          onConfirm={handleConfirmChanges}
        />
      )}
    </div>
  );
}
